package net.summerfarm.wnc.application.inbound.scheduler.changeTask;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.FenceChangTaskHandleService;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 普通围栏切仓区域处理任务
 */
@Slf4j
@Component
public class CustomFenceChangeAreaHandleJob extends XianMuJavaProcessorV2 {

    @Resource
    private FenceChangTaskHandleService fenceChangTaskHandleService;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        //城配仓截单时间晚20点、20点半、21点、21点半、22点、22点半、23点、23点半、次日0点、0点半执行
        log.info("自定义围栏切仓区域处理任务开始执行");

        // 获取当天已到执行时间、状态为等待生效的城市区域记录
        LocalDateTime now = LocalDateTime.now();
        Integer changeStatus = WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue();
        Integer areaDefinationType = WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.CUSTOM.getValue();
        List<WncCityAreaChangeWarehouseRecordsEntity> executableRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectExecutableRecords(now, changeStatus,areaDefinationType);

        if (CollectionUtils.isEmpty(executableRecords)) {
            log.info("围栏切仓区域处理任务，无可执行切仓任务");
            return new ProcessResult(true,"自定义围栏切仓区域处理任务，无可执行切仓任务");
        }
        Map<String, List<WncCityAreaChangeWarehouseRecordsEntity>> changeBatchNoMap = executableRecords.stream().collect(Collectors.groupingBy(WncCityAreaChangeWarehouseRecordsEntity::getChangeBatchNo));

        changeBatchNoMap.forEach((changeBatchNo, records) -> {
            // 每个changeBatchNo作为一个独立的事务处理
            fenceChangTaskHandleService.executeCustomFenceChangeAreaHandleByBatch(changeBatchNo, records);
        });
        return new ProcessResult(true);
    }
}
