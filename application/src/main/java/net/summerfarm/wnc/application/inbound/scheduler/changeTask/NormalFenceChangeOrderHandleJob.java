package net.summerfarm.wnc.application.inbound.scheduler.changeTask;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.changeTask.service.FenceChangeTaskService;
import net.summerfarm.wnc.application.service.changeTask.FenceChangTaskForOrderChangeHandleService;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 围栏切仓订单处理任务
 * date: 2023/8/30 11:22
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NormalFenceChangeOrderHandleJob extends XianMuJavaProcessorV2 {

    @Resource
    private FenceChangTaskForOrderChangeHandleService fenceChangTaskForOrderChangeHandleService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 切仓次日凌晨3/4/5点执行
        log.info("普通围栏切仓订单处理任务开始执行");

        // 查询当前时间点可执行(已过执行时间)且处于待处理的普通围栏的切仓任务
        List<FenceChangeTaskEnums.Type> types = Arrays.asList(FenceChangeTaskEnums.Type.FENCE, FenceChangeTaskEnums.Type.STORE);
        List<FenceChangeTaskEntity> waitHandleTasks = fenceChangeTaskRepository.queryExecutableTask(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING,types);

        if (CollectionUtils.isEmpty(waitHandleTasks)){
            return new ProcessResult(true,"普通围栏切仓区域订单处理任务，无可处理切仓订单任务");
        }

        // 查询新模型是否有数据
        List<Long> fenceChangeTaskIds = waitHandleTasks.stream().map(FenceChangeTaskEntity::getId).collect(Collectors.toList());
        Map<Long, List<WncCityAreaChangeWarehouseRecordsEntity>> fenceChangeTaskIdMap = wncCityAreaChangeWarehouseRecordsQueryRepository.selectTaskIdMapByFenceChangeTaskIds(fenceChangeTaskIds);

        for (FenceChangeTaskEntity waitOrderChangeHandleTask : waitHandleTasks) {
            if (!fenceChangeTaskIdMap.containsKey(waitOrderChangeHandleTask.getId())){
                log.info("围栏切仓区域处理任务-任务编号：{}，旧功能处理会去处理，本次新模型无需处理", waitOrderChangeHandleTask.getId());
                continue;
            }
            fenceChangTaskForOrderChangeHandleService.normalFenceChangeOrderHandle(waitOrderChangeHandleTask);
        }
        return new ProcessResult(true);
    }
}
