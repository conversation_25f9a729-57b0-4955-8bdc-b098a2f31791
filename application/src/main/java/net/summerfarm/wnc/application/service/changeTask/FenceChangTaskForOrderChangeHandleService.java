package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 切仓任务订单处理服务
 * date: 2025/9/5 10:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;

    /**
     * 普通围栏切仓订单处理
     */
    public void normalFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }
        FulfillmentQueryInput input = FulfillmentQueryInput.builder()
                .city(waitOrderChangeHandleTask.getFenceCityNameStr())
                .areas(waitOrderChangeHandleTask.getFenceAreasName())
                .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date()).build();

        //查询T+1之后待履约的履约单数据
        List<FulfillmentOrderDTO> waitHandleFulfillmentOrderDTOList = Collections.emptyList();
        try {
            List<FulfillmentOrderDTO> fulfillmentOrderDTOList = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(input);
            waitHandleFulfillmentOrderDTOList = this.filterWaitHandleFulfillmentOrder(fulfillmentOrderDTOList, waitOrderChangeHandleTask);
        }catch (Throwable e){
            log.error("围栏切仓订单处理任务-任务编号：{}，调用OFC接口查询T+1之后待履约的履约单数据异常，等待下一次调度", waitOrderChangeHandleTask.getId(), e);
            fenceChangeTaskSender.sendOrderFailMsg(waitOrderChangeHandleTask, null);
        }

        if (CollectionUtils.isEmpty(waitHandleFulfillmentOrderDTOList)){
            log.info("围栏切仓订单处理任务-任务编号：{}，未查询到待处理的履约单明细集合", waitOrderChangeHandleTask.getId());
            //无履约单需要处理可直接更新切仓任务状态为已完成
            FenceChangeTaskEntity updateComplete = waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED);
            fenceChangeTaskRepository.update(updateComplete);
        }

        log.info("围栏切仓订单处理任务-任务编号：{}，待处理的履约单明细集合：{}，条数：{}", waitOrderChangeHandleTask.getId(),
                JSON.toJSONString(waitHandleFulfillmentOrderDTOList), waitHandleFulfillmentOrderDTOList.size());


        Set<FenceChangeTaskOrderEntity> waitHandleTaskOrders = new HashSet<>();
        try {
            //合并履约单处理
            waitHandleTaskOrders = waitHandleFulfillmentOrderDTOList.stream().filter(e -> e.getSource() != null).map(e -> {
                FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(e);
                fenceChangeTaskOrderEntity.create(waitOrderChangeHandleTask.getId());
                return fenceChangeTaskOrderEntity;
            }).collect(Collectors.toSet());
            log.info("围栏切仓订单处理任务-任务编号：{}，合并后的履约单明细集合：{}，条数：{}", waitOrderChangeHandleTask.getId(),JSON.toJSONString(waitHandleFulfillmentOrderDTOList), waitHandleFulfillmentOrderDTOList.size());
            fenceChangeTaskDomainService.saveBatchDetail(waitHandleTaskOrders);
        }catch (Throwable e){
            if (e instanceof DuplicateKeyException){
                log.error("围栏切仓订单处理任务-任务编号：{}，切仓订单明细批量插入UK异常，无需重复处理", waitOrderChangeHandleTask.getId());
            }

            //存在订单切仓失败 发送飞书消息通知
            fenceChangeTaskSender.sendOrderFailMsg(waitOrderChangeHandleTask, waitHandleFulfillmentOrderDTOList.size());
        }

        List<String> failOrders = new ArrayList<>();
        for (FenceChangeTaskOrderEntity waitHandleTaskOrder : waitHandleTaskOrders) {
            try {
                fenceChangeTaskDomainService.doFenceChangeOrderHandle(waitOrderChangeHandleTask, waitHandleTaskOrder);
            }catch (Throwable e){
                failOrders.add(waitHandleTaskOrder.getOuterOrderId());
                log.info("围栏切仓订单处理任务-履约单处理失败，等待失败订单重试，异常原因：{}", e.getMessage(), e);
                FenceChangeTaskOrderEntity updateOrderFail = waitHandleTaskOrder.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
                fenceChangeTaskDetailRepository.update(updateOrderFail);
            }
        }
        //履约单处理完成需要直接更新切仓任务状态为已完成
        fenceChangeTaskDomainService.updateFenceChangeTaskStatus(Collections.singletonList(waitOrderChangeHandleTask.getId()),
                FenceChangeTaskEnums.Status.ORDER_CHANGE_ING.getValue(),
                FenceChangeTaskEnums.Status.COMPLETED.getValue());

        if (!failOrders.isEmpty()){
            //存在订单切仓失败 发送飞书消息通知
            fenceChangeTaskSender.sendOrderFailMsg(waitOrderChangeHandleTask, failOrders.size());
        }
    }

    private List<FulfillmentOrderDTO> filterWaitHandleFulfillmentOrder(List<FulfillmentOrderDTO> fulfillmentOrderDTOList, FenceChangeTaskEntity fenceChangeTaskEntity) {
        if (CollectionUtils.isEmpty(fulfillmentOrderDTOList)){
            return Collections.emptyList();
        }
        Integer newStoreNo = fenceChangeTaskEntity.getFenceChangeRemarkVO().getNewStoreNo();
        //过滤出执行切仓区域之后且非新仓履约的单子
        List<FulfillmentOrderDTO> waitHandleOrders = fulfillmentOrderDTOList.stream().filter(e -> !Objects.equals(e.getStoreNo(), newStoreNo)).collect(Collectors.toList());
        List<String> outOrderIds = waitHandleOrders.stream().map(FulfillmentOrderDTO::getOuterOrderId).collect(Collectors.toList());
        log.info("围栏切仓订单处理任务-任务编号：{}，执行切仓区域之后且非新仓履约的履约单明细集合：{}", fenceChangeTaskEntity.getId(), JSON.toJSONString(outOrderIds));
        return waitHandleOrders;
    }
}
