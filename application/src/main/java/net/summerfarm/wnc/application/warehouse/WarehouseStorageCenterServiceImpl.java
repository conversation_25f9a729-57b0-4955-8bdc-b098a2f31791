package net.summerfarm.wnc.application.warehouse;

import com.alibaba.fastjson.JSONObject;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.config.dto.WarehouseConfigCabinetStatusDTO;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseBaseDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.input.*;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseStorageCenterService;
import net.summerfarm.wnc.application.fence.converter.WarehouseStorageEntityConverter;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseMessageConverter;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseStorageCenterConverter;
import net.summerfarm.wnc.application.warehouse.converter.WarehouseStorageCenterEntityConverter;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.client.mq.msg.WarehouseStorageCreateMsg;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.common.enums.WarehouseStorageCenterEnums;
import net.summerfarm.wnc.common.enums.WncTenantGlobalFenceRuleEnums;
import net.summerfarm.wnc.common.message.WarehouseStorageCenterUpdateMessage;
import net.summerfarm.wnc.common.query.warehouse.WarehouseInventoryMappingQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.common.util.DistanceUtil;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.WncTenantGlobalFenceRuleRepository;
import net.summerfarm.wnc.domain.fence.WncWarehouseStorageFenceRuleDomainService;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.*;
import net.summerfarm.wnc.domain.warehouse.entity.*;
import net.summerfarm.wnc.facade.auth.AdminDataPermissionQueryFacade;
import net.summerfarm.wnc.facade.auth.AuthQueryFacade;
import net.summerfarm.wnc.facade.saas.SaasTenantQueryFacade;
import net.summerfarm.wnc.facade.saas.dto.TenantDTO;
import net.summerfarm.wnc.facade.wms.WarehouseNameRefreshFacade;
import net.summerfarm.wnc.facade.wms.WmsQueryFacade;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.function.Function;
import java.math.BigDecimal;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/30 19:02<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseStorageCenterServiceImpl implements WarehouseStorageCenterService {

    private final WarehouseStorageCenterDomainService warehouseStorageCenterDomainService;
    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    private final SaasTenantQueryFacade saasTenantQueryFacade;
    private final WncWarehouseStorageFenceRuleDomainService wncWarehouseStorageFenceRuleDomain;
    private final WncTenantGlobalFenceRuleRepository wncTenantGlobalFenceRuleRepository;
    private final WncWarehouseStorageFenceRuleDomainService wncWarehouseStorageFenceRuleDomainService;
    private final AdminRepository adminRepository;
    private final DeliveryFenceRepository deliveryFenceRepository;
    private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
    private final WarehouseStorageCenterConverter warehouseStorageCenterConverter;
    private final WmsWarehouseConfigRepository wmsWarehouseConfigRepository;
    private final MqProducer mqProducer;
    private final WarehouseNameRefreshFacade warehouseNameRefreshFacade;
    private final WmsQueryFacade wmsQueryFacade;
    private final AuthQueryFacade authQueryFacade;
    private final WncConfig wncConfig;
    private final AdminDataPermissionQueryFacade adminDataPermissionQueryFacade;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public WarehouseStorageDTO warehouseStorageSave(WarehouseStorageSaveInput warehouseStorageSaveCommandInput) {
        //保存逻辑
        WarehouseStorageEntity warehouseStorageEntity = WarehouseStorageCenterEntityConverter.warehouseStorageSave2Entity(warehouseStorageSaveCommandInput);
        warehouseStorageCenterDomainService.save(warehouseStorageEntity);
        //判断租户是否存在全局的配送规则
        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity = wncTenantGlobalFenceRuleRepository.queryUk(warehouseStorageSaveCommandInput.getTenantId());
        if(wncTenantGlobalFenceRuleEntity == null){
            wncTenantGlobalFenceRuleEntity = new WncTenantGlobalFenceRuleEntity();
            wncTenantGlobalFenceRuleEntity.setTenantId(warehouseStorageSaveCommandInput.getTenantId());
            wncTenantGlobalFenceRuleEntity.setGlobalDeliveryRule(WncTenantGlobalFenceRuleEnums.deliveryRule.SELF_FIRST.getValue());
            wncTenantGlobalFenceRuleRepository.save(wncTenantGlobalFenceRuleEntity);
        }

        return WarehouseStorageCenterEntityConverter.entity2DTO(warehouseStorageEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void warehouseStorageUpdate(WarehouseStorageUpdateInput warehouseStorageUpdateInput) {
        WarehouseStorageEntity oldWarehouseStorage = warehouseStorageCenterRepository.queryById(Long.parseLong(String.valueOf(warehouseStorageUpdateInput.getId())));
        if(oldWarehouseStorage == null){
            throw new BizException("数据不存在");
        }
        if(!Objects.equals(oldWarehouseStorage.getTenantId(),warehouseStorageUpdateInput.getTenantId())){
            throw new BizException("数据不存在");
        }
        //更新逻辑
        warehouseStorageCenterDomainService.updateStorage(WarehouseStorageCenterEntityConverter.warehouseStorageUpdate2Entity(warehouseStorageUpdateInput));
        //仓库名称是否有变化
        if(!oldWarehouseStorage.getWarehouseName().equals(warehouseStorageUpdateInput.getWarehouseName())){
            //有变化需要修改规则里面的名称
            wncWarehouseStorageFenceRuleDomainService.updateWarehouseName(oldWarehouseStorage.getWarehouseNo(),
                    oldWarehouseStorage.getWarehouseName(),
                    warehouseStorageUpdateInput.getWarehouseName(),
                    oldWarehouseStorage.getTenantId());
        }
    }

    @Override
    public WarehouseStorageDTO queryDetail(Integer id) {
        return WarehouseStorageCenterEntityConverter.entity2DTO(warehouseStorageCenterRepository.queryDetail(id));
    }

    @Override
    public PageInfo<WarehouseStorageDTO> queryPage(WarehouseStorageQuery warehouseStorageQuery) {
        PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo = new PageInfo<>();
        PageInfo<WarehouseStorageEntity> warehouseStorageEntityPageInfo = new PageInfo<>();
        String warehouseServiceName = new String();
        if(Objects.equals(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode(),warehouseStorageQuery.getWarehouseSource())){
            //查询代仓信息
            warehouseStorageEntityPageInfo = warehouseStorageCenterRepository.queryProxyWarehouseStoragePage(warehouseStorageQuery);
            warehouseServiceName = AppConsts.WarehouseServiceName.xianMu;
        }else{
            //查询自营仓信息
            warehouseStorageEntityPageInfo = warehouseStorageCenterRepository.querySelfWarehouseStoragePage(warehouseStorageQuery);
            //查询租户的工商名称
            TenantDTO tenantDTO = saasTenantQueryFacade.queryTenantInfo(warehouseStorageQuery.getTenantId());
            if(tenantDTO != null){
                warehouseServiceName = tenantDTO.getCompanyName();
            }
        }

        BeanUtils.copyProperties(warehouseStorageEntityPageInfo, warehouseStorageDTOPageInfo);

        if(!CollectionUtils.isEmpty(warehouseStorageEntityPageInfo.getList())){
            warehouseStorageDTOPageInfo.setList(warehouseStorageEntityPageInfo.getList().stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList()));
            String finalWarehouseServiceName = warehouseServiceName;
            warehouseStorageDTOPageInfo.getList().forEach(warehouse ->{
                warehouse.setWarehouseServiceName(finalWarehouseServiceName);
            });
        }
        return warehouseStorageDTOPageInfo;
    }

    @Override
    public WarehouseStorageDTO queryByWarehouseNo(Integer warehouseNo) {
        WarehouseStorageEntity warehouseStorageEntity = warehouseStorageCenterRepository.queryByWarehouseNo(warehouseNo);
        WarehouseStorageDTO warehouseStorageDTO = this.queryDetail(warehouseStorageEntity.getId());
        if(warehouseStorageDTO.getTenantId().intValue() == WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode()){
            warehouseStorageDTO.setWarehouseServiceName(AppConsts.WarehouseServiceName.xianMu);
        }else{
            //查询租户的工商名称
            TenantDTO tenantDTO = saasTenantQueryFacade.queryTenantInfo(warehouseStorageDTO.getTenantId());
            if(tenantDTO != null){
                warehouseStorageDTO.setWarehouseServiceName(tenantDTO.getCompanyName());
            }
        }

        return warehouseStorageDTO;
    }

    @Override
    public List<WarehouseStorageDTO> queryList(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterDomainService.queryDetailList(warehouseStorageQuery);
        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageEntities.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(warehouseStorageDTOS)){
            return Collections.emptyList();
        }
        List<Long> tenantIds = warehouseStorageDTOS.stream()
                .map(WarehouseStorageDTO::getTenantId)
                .filter(tenantId -> !Objects.equals(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode(), tenantId.intValue())).collect(Collectors.toList());

        warehouseStorageDTOS.forEach(warehouseStorageDTO -> {
            if(warehouseStorageDTO.getTenantId().intValue() == WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode()){
                warehouseStorageDTO.setWarehouseServiceName(AppConsts.WarehouseServiceName.xianMu);
            }
        });
        if(CollectionUtils.isEmpty(tenantIds)){
            return warehouseStorageDTOS;
        }

        //查询租户的工商名称
        List<TenantDTO> tenantDTOS = saasTenantQueryFacade.queryTenantInfoList(tenantIds);
        Map<Long, String> tenantIdMap = tenantDTOS.stream().collect(Collectors.toMap(TenantDTO::getId, TenantDTO::getCompanyName, (oldValue, newValue) -> newValue));
        warehouseStorageDTOS.forEach(warehouseStorageDTO -> {
            if(warehouseStorageDTO.getTenantId().intValue() != WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode()){
                String companyName = tenantIdMap.get(warehouseStorageDTO.getTenantId());
                if(StrUtil.isNotBlank(companyName)){
                    warehouseStorageDTO.setWarehouseServiceName(companyName);
                }
            }
        });

        return warehouseStorageDTOS;
    }

    @Override
    public List<WarehouseStorageDTO> queryWarehouseList(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageEntity> warehouseStorageEntityList = new ArrayList<>();
        if(Objects.equals(warehouseStorageQuery.getWarehouseSource(),WarehouseSourceEnum.SAAS_WAREHOUSE.getCode())){
            //查询自营仓
            List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterDomainService.querySelffList(warehouseStorageQuery);
            warehouseStorageEntityList.addAll(warehouseStorageEntities);
        }else if(Objects.equals(warehouseStorageQuery.getWarehouseSource(),WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode())){
            //查询代仓
            List<WarehouseStorageEntity> warehouseStorageProxyEntities = warehouseStorageCenterDomainService.queryProxyList(warehouseStorageQuery);
            warehouseStorageEntityList.addAll(warehouseStorageProxyEntities);
        }else{
            //查询自营仓
            List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterDomainService.querySelffList(warehouseStorageQuery);
            //查询代仓
            List<WarehouseStorageEntity> warehouseStorageProxyEntities = warehouseStorageCenterDomainService.queryProxyList(warehouseStorageQuery);
            warehouseStorageEntityList.addAll(warehouseStorageProxyEntities);
            warehouseStorageEntityList.addAll(warehouseStorageEntities);
        }

        return warehouseStorageEntityList.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());
    }

    @Override
    public PageInfo<WarehouseStorageDTO> queryFenceList(WarehouseStorageFenceQuery warehouseStorageFenceQuery) {
        WarehouseStorageQuery warehouseStorageQuery = WarehouseStorageQuery.builder()
                .tenantId(warehouseStorageFenceQuery.getTenantId())
                .provinces(warehouseStorageFenceQuery.getProvinces())
                .citys(warehouseStorageFenceQuery.getCitys())
                .areas(warehouseStorageFenceQuery.getAreas())
                .warehouseName(warehouseStorageFenceQuery.getWarehouseName())
                .warehouseSource(WarehouseSourceEnum.SAAS_WAREHOUSE.getCode())
                .build();

        warehouseStorageQuery.setPageIndex(warehouseStorageFenceQuery.getPageIndex());
        warehouseStorageQuery.setPageSize(warehouseStorageFenceQuery.getPageSize());
        warehouseStorageQuery.setDesc(2);

        PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo = new PageInfo<>();

        //查询自营仓信息
        PageInfo<WarehouseStorageEntity> warehouseStorageEntityPageInfo = warehouseStorageCenterRepository.queryFencePageList(warehouseStorageQuery);
        //查询租户的工商名称
        TenantDTO tenantDTO = saasTenantQueryFacade.queryTenantInfo(warehouseStorageQuery.getTenantId());
        String warehouseServiceName = new String();
        if(tenantDTO != null){
            warehouseServiceName = tenantDTO.getCompanyName();
        }
        BeanUtils.copyProperties(warehouseStorageEntityPageInfo, warehouseStorageDTOPageInfo);

        if(!CollectionUtils.isEmpty(warehouseStorageEntityPageInfo.getList())){
            warehouseStorageDTOPageInfo.setList(warehouseStorageEntityPageInfo.getList().stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList()));
            String finalWarehouseServiceName = warehouseServiceName;
            warehouseStorageDTOPageInfo.getList().forEach(warehouse ->{
                warehouse.setWarehouseServiceName(finalWarehouseServiceName);
            });
        }
        return warehouseStorageDTOPageInfo;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void fenceEdit(WarehouseFenceEditInput warehouseFenceEditInput) {
        //根据Id查询仓库信息
        WarehouseStorageEntity warehouseStorageEntity = warehouseStorageCenterRepository.queryById(warehouseFenceEditInput.getId());
        if(warehouseStorageEntity == null){
            throw new BizException("不存在此仓库信息");
        }

        List<WncWarehouseStorageFenceEntity> fenceEntities = new ArrayList<>();
        if(!CollectionUtils.isEmpty(warehouseFenceEditInput.getFenceList())){
            for (WarehouseStorageFenceSaveInput warehouseStorageFenceSaveInput : warehouseFenceEditInput.getFenceList()) {
                WncWarehouseStorageFenceEntity wncWarehouseStorageFenceEntity = new WncWarehouseStorageFenceEntity();
                wncWarehouseStorageFenceEntity.setTenantId(warehouseStorageEntity.getTenantId());
                wncWarehouseStorageFenceEntity.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
                wncWarehouseStorageFenceEntity.setProvince(warehouseStorageFenceSaveInput.getProvince());
                wncWarehouseStorageFenceEntity.setCity(warehouseStorageFenceSaveInput.getCity());
                wncWarehouseStorageFenceEntity.setArea(warehouseStorageFenceSaveInput.getArea());
                wncWarehouseStorageFenceEntity.setLastOperatorName(warehouseFenceEditInput.getOperatorName());

                fenceEntities.add(wncWarehouseStorageFenceEntity);
            }
        }

        warehouseStorageEntity.setWarehouseStorageFenceEntities(fenceEntities);
        //冲突区域处理
        wncWarehouseStorageFenceRuleDomain.fenceConflictRule(warehouseStorageEntity);
    }

    @Override
    public WarehouseStorageDTO detailWarehouseNo(Integer warehouseNo) {
        WarehouseStorageEntity warehouseStorageEntity = warehouseStorageCenterRepository.queryByWarehouseNo(warehouseNo);
        if (warehouseStorageEntity == null) {
            throw new BizException("不存在此数据");
        }
        WarehouseStorageEntity warehouseStorage = warehouseStorageCenterRepository.queryDetail(warehouseStorageEntity.getId());
        //鲜沐仓查询仓库负责人
        if (Objects.equals(warehouseStorage.getTenantId().intValue(), WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode())) {
            warehouseStorage.setAdminEntity(AdminEntity.builder().manageAdminName(adminRepository.queryNameById(warehouseStorageEntity.getManageAdminId())).build());
        }

        return WarehouseStorageCenterEntityConverter.entity2DTO(warehouseStorage);
    }

    @Override
    public List<WarehouseStorageDTO> queryXmWarehouseBySkuCity(WarehouseStorageQuery warehouseStorageQuery) {
        //根据城市查询对应的城配仓
        List<FenceEntity> fenceEntities = deliveryFenceRepository.queryListFenceByCity(warehouseStorageQuery.getCity());
        if(CollectionUtils.isEmpty(fenceEntities)){
            return Collections.emptyList();
        }
        ArrayList<WarehouseStorageDTO> warehouseStorageDTOS = new ArrayList<>();

        //因为城市区域可能会存在多个不一样城配仓，所以要单个查询
        for (String sku : warehouseStorageQuery.getSkuList()) {
            //根据sku和城配查询仓库信息
            List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                    .storeNos(fenceEntities.stream().map(FenceEntity::getStoreNo).collect(Collectors.toList()))
                    .sku(sku)
                    .build());
            if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
                WarehouseStorageDTO warehouseStorageDTO = new WarehouseStorageDTO();
                warehouseStorageDTO.setSku(sku);
                warehouseStorageDTOS.add(warehouseStorageDTO);
                continue;
            }

            for (WarehouseInventoryMappingEntity warehouseInventoryMappingEntity : warehouseInventoryMappingEntities) {
                WarehouseStorageDTO warehouseStorageDTO = new WarehouseStorageDTO();
                warehouseStorageDTO.setWarehouseNo(warehouseInventoryMappingEntity.getWarehouseNo());
                warehouseStorageDTO.setSku(sku);
                warehouseStorageDTOS.add(warehouseStorageDTO);
            }
        }

        return warehouseStorageDTOS;
    }
    

    @Override
    public List<WarehouseBaseDTO> queryBaseInfoByNos(List<Integer> warehouseNos) {
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return null;
        }
        WarehouseStorageQuery warehouseStorageQuery = new WarehouseStorageQuery();
        warehouseStorageQuery.setWarehouseNos(warehouseNos);
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryList(warehouseStorageQuery);
        return warehouseStorageCenterConverter.entityToBaseDTO(warehouseStorageEntities);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateXmWarehouseStorage(XmWarehouseStorageUpdateInput xmWarehouseStorage) {
        WarehouseStorageEntity warehouseStorageEntity = WarehouseStorageCenterEntityConverter.xmWarehouseStorageUpdateInput2Entity(xmWarehouseStorage);
        //校验信息
        verifyXmWarehouseStorage(warehouseStorageEntity);
        warehouseStorageEntity.setCreator(UserInfoHolder.getUser().getBizUserId());
        //历史仓库信息
        WarehouseStorageEntity oldWarehouseStorage = warehouseStorageCenterRepository.queryDetail(xmWarehouseStorage.getId());
        //更新逻辑
        warehouseStorageEntity.setUpdater(UserInfoHolder.getUser().getBizUserId());
        warehouseStorageCenterDomainService.updateStorage(warehouseStorageEntity);
        //最新仓库信息
        WarehouseStorageEntity newWarehouseStorage = warehouseStorageCenterRepository.queryDetail(xmWarehouseStorage.getId());
        //事务之后发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                WarehouseStorageCenterUpdateMessage warehouseStorageCenterUpdateMessage = WarehouseMessageConverter.oldNewWarehouse2Message(oldWarehouseStorage, newWarehouseStorage);
                warehouseStorageCenterUpdateMessage.setTenantId(newWarehouseStorage.getTenantId());
                try {
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_WAREHOUSE,WncMqConstants.Tag.TAG_WNC_WAREHOUSE_UPDATE,warehouseStorageCenterUpdateMessage);
                } catch (Throwable e) {
                    log.error("发送更新鲜沐仓库Mq消息异常:{}", JSONObject.toJSONString(warehouseStorageCenterUpdateMessage), e);
                }
                try {
                    warehouseNameRefreshFacade.refresh(newWarehouseStorage.getWarehouseNo(),newWarehouseStorage.getWarehouseName());
                } catch (Exception e) {
                    log.error("调用WMS更新鲜沐仓库异常:{},{}", newWarehouseStorage.getWarehouseNo(),newWarehouseStorage.getWarehouseName(), e);
                }
            }
        });
    }

    private void verifyXmWarehouseStorage(WarehouseStorageEntity warehouseStorage) {
        if(warehouseStorage == null){
            throw new BizException("请求信息不能为空");
        }
        if (StringUtils.isBlank(warehouseStorage.getWarehouseName()) || !warehouseStorage.getWarehouseName().matches("^[一-龥]{1,10}$")) {
            throw new BizException("仓库名称不符合要求");
        }
        long nameCount = warehouseStorageCenterRepository.queryXmCountByNameAndId(warehouseStorage.getWarehouseName(),warehouseStorage.getId());
        if (nameCount > 0) {
            throw new BizException("仓库名称重复");
        } else if (Objects.equals(WarehouseStorageCenterEnums.Type.PARTNER_AREA.getValue(), warehouseStorage.getType()) && warehouseStorage.getManageAdminId() == null) {
            throw new BizException("合伙人仓请先选择所属合伙人");
        }
        WarehouseStorageCenterBusEntity bus = warehouseStorage.getWarehouseStorageCenterBusEntity();
        if (bus != null) {
            if (bus.getAdvanceDay() < 0 || bus.getAdvanceDay() > 3) {
                throw new BizException("预约提前期只能在3天以内");
            }
        }

        List<WarehouseStorageCenterWorkEntity> works = warehouseStorage.getWarehouseStorageCenterWorkEntities();
        if(works != null){
            for (int i = 0; i < works.size(); i++) {
                if(works.get(i).getWorkStartTime() == null || works.get(i).getWorkEndTime() == null){
                    throw new BizException("开始时间或结束时间不能为空");
                }
                if(works.get(i).getWorkStartTime().isAfter(works.get(i).getWorkEndTime())){
                    throw new BizException("开始时间不能大于结束时间");
                }

                for (int j = i + 1; j < works.size(); j++) {
                    if(works.get(j).getWorkStartTime() == null || works.get(j).getWorkEndTime() == null){
                        throw new BizException("开始时间或结束时间不能为空");
                    }
                    if(works.get(i).getWorkStartTime().isAfter(works.get(j).getWorkEndTime()) || works.get(i).getWorkEndTime().isBefore(works.get(j).getWorkStartTime())) {

                    }else{
                        // 两个时间段有重叠
                        throw new BizException("有时间段重叠,不能保存");
                    }
                }
            }

        }

    }

    @Override
    public PageInfo<WarehouseStorageDTO> queryXmWarehousePageList(WarehouseStorageQuery warehouseStorageQuery) {
        PageInfo<WarehouseStorageEntity> warehouseStorageEntityPageInfo = warehouseStorageCenterRepository.queryWarehouseStoragePage(warehouseStorageQuery);
        //查询仓库配置信息
        PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(warehouseStorageEntityPageInfo, warehouseStorageDTOPageInfo);

        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageEntityPageInfo.getList();
        if(CollectionUtils.isEmpty(warehouseStorageEntities)){
           return warehouseStorageDTOPageInfo;
        }
        List<WarehouseStorageDTO> warehouseStorageDTOs = warehouseStorageEntities.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());
        //查询配置信息
        List<Integer> warehouseNos = warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList());
        //查询名称
        List<Integer> manageAdminIds = warehouseStorageDTOs.stream().map(WarehouseStorageDTO::getManageAdminId).collect(Collectors.toList());
        Map<Integer, String> adminNameMap = adminRepository.queryByIds(manageAdminIds);

        List<WmsWarehouseConfigEntity> wmsWarehouseConfigs = wmsWarehouseConfigRepository.queryCabinetByWarehouseNos(warehouseNos);
        if(!CollectionUtils.isEmpty(wmsWarehouseConfigs)){
            Map<Integer, String> cabinetStatusMap = wmsWarehouseConfigs.stream().collect(Collectors.toMap(WmsWarehouseConfigEntity::getWarehouseNo, WmsWarehouseConfigEntity::getConfigValue, (o1, o2) -> o2));
            warehouseStorageDTOs.forEach(warehouseDto -> {
                if(cabinetStatusMap.get(warehouseDto.getWarehouseNo()) != null){
                    warehouseDto.setCabinetStatus(cabinetStatusMap.get(warehouseDto.getWarehouseNo()));
                }
            });
        }
        warehouseStorageDTOs.forEach(warehouseDto -> {
            warehouseDto.setManageAdminName(adminNameMap.get(warehouseDto.getManageAdminId()));
        });

        warehouseStorageDTOPageInfo.setList(warehouseStorageDTOs);
        return warehouseStorageDTOPageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveXmWarehouse(XmWarehouseStorageSaveInput xmWarehouseStorageSaveInput) {
        //新增逻辑
        WarehouseStorageEntity warehouseStorageEntity = WarehouseStorageCenterEntityConverter.xmWarehouseStorageSave2Entity(xmWarehouseStorageSaveInput);
        //校验信息
        verifyXmWarehouseStorage(warehouseStorageEntity);
        warehouseStorageCenterDomainService.saveXmWarehouse(warehouseStorageEntity);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                WarehouseStorageCreateMsg warehouseStorageCreateMsg = new WarehouseStorageCreateMsg();
                try {
                    //发送消息
                    warehouseStorageCreateMsg.setTenantId(warehouseStorageEntity.getTenantId());
                    warehouseStorageCreateMsg.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
                    warehouseStorageCreateMsg.setWarehouseName(warehouseStorageEntity.getWarehouseName());
                    warehouseStorageCreateMsg.setPoiNote(warehouseStorageEntity.getPoiNote());
                    warehouseStorageCreateMsg.setAddress(warehouseStorageEntity.getAddress());
                    warehouseStorageCreateMsg.setPersonContact(warehouseStorageEntity.getPersonContact());
                    warehouseStorageCreateMsg.setPhone(warehouseStorageEntity.getPhone());
                    warehouseStorageCreateMsg.setWarehousePic(warehouseStorageEntity.getWarehousePic());
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_WAREHOUSE,WncMqConstants.Tag.TAG_WNC_WAREHOUSE_CREATE,warehouseStorageCreateMsg);
                } catch (Throwable e) {
                    log.error("新增鲜沐仓同步初始化发送消息:{}", JSONObject.toJSONString(warehouseStorageCreateMsg), e);
                }
                try {
                    warehouseNameRefreshFacade.refresh(warehouseStorageEntity.getWarehouseNo(),warehouseStorageEntity.getWarehouseName());
                } catch (Exception e) {
                    log.error("调用WMS新增鲜沐仓库异常:{},{}", warehouseStorageEntity.getWarehouseNo(),warehouseStorageEntity.getWarehouseName(), e);
                }
            }
        });
    }

    @Override
    public List<WarehouseStorageDTO> queryWarehouseListByInfo(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryListWithBusinessWorkStandard(warehouseStorageQuery);
        return warehouseStorageEntities.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());
    }

    @Override
    public List<WncWarehouseStorageFenceRuleDTO> queryWarehouseStorageStoreFence(WarehouseStorageFenceQueryReq warehouseStorageFenceQueryReq) {
        //根据sku和城配仓编号查询对应的城配配送信息
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .skus(warehouseStorageFenceQueryReq.getSkuList())
                .storeNo(warehouseStorageFenceQueryReq.getStoreNo())
                .build());
        if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
            return Collections.emptyList();
        }

        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder()
                .warehouseNos(warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingEntity::getWarehouseNo).collect(Collectors.toList()))
                .status(WarehouseStorageCenterEnums.Status.OPEN.getValue())
                .build());
        for (WarehouseStorageEntity warehouseStorageEntity : warehouseStorageEntityList) {
            String poiNote = warehouseStorageEntity.getPoiNote();
            warehouseStorageEntity.setStoreNoList(Collections.singletonList(warehouseStorageFenceQueryReq.getStoreNo()));
            if(StringUtils.isNotBlank(poiNote) && StringUtils.isNotBlank(warehouseStorageFenceQueryReq.getPoi())){
                try {
                    warehouseStorageEntity.setDistance(new BigDecimal(DistanceUtil.getPoiDistance(poiNote, warehouseStorageFenceQueryReq.getPoi())).setScale(2,BigDecimal.ROUND_HALF_UP));
                } catch (Exception e) {
                    log.error("计算到仓库距离异常",e);
                }
            }else{
                warehouseStorageEntity.setDistance(new BigDecimal(0));
            }
        }
        return warehouseStorageEntityList.stream().map(WarehouseStorageEntityConverter::entity2FenceRuleDTO).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseStorageDTO> queryXmThirdAllWarehouseList() {
        //查询所有的开放的库存仓
        List<WarehouseStorageEntity> warehouseStorageOpenList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder().status(WarehouseStorageCenterEnums.Status.OPEN.getValue()).build());
        if(CollectionUtils.isEmpty(warehouseStorageOpenList)){
            return Collections.emptyList();
        }
        Set<Long> tenantIdSet = warehouseStorageOpenList.stream().map(WarehouseStorageEntity::getTenantId).collect(Collectors.toSet());
        List<TenantDTO> tenants = saasTenantQueryFacade.queryTenantInfoList(new ArrayList<>(tenantIdSet));
        Map<Long, TenantDTO> tenantIdMap = tenants.stream().collect(Collectors.toMap(TenantDTO::getId, Function.identity(), (oldData, newData) -> newData));

        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageOpenList.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());
        warehouseStorageDTOS.forEach(warehouseDTO->{
            if(Objects.equals(warehouseDTO.getTenantId(),Long.parseLong(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode().toString()))){
                warehouseDTO.setTenantName("杭州鲜沐科技有限公司");
            }else{
                warehouseDTO.setTenantName(tenantIdMap.get(warehouseDTO.getTenantId()) != null ? tenantIdMap.get(warehouseDTO.getTenantId()).getTenantName() : "");
            }
        });

        return warehouseStorageDTOS;
    }

    @Override
    public List<WarehouseStorageDTO> queryXmWarehouseList(WarehouseStorageQuery warehouseStorageQuery) {
        // 查询鲜沐自营仓配置
        List<Integer> selfWarehouseNos = wncConfig.querySelfWarehouseNos();
        if(warehouseStorageQuery.getSelfWarehouseFlag() != null){
            if(warehouseStorageQuery.getSelfWarehouseFlag()){// NOSONAR
                warehouseStorageQuery.setSelfWarehouseNos(selfWarehouseNos);
            }else{
                warehouseStorageQuery.setNoInSelfWarehouseNos(selfWarehouseNos);
            }
        }
        List<WarehouseStorageDTO> warehouseStorageDTOList = warehouseStorageCenterRepository.queryList(warehouseStorageQuery).stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());

        return warehouseStorageDTOList.stream()
                .peek(dto -> dto.setSelfWarehouseFlag(selfWarehouseNos.contains(dto.getWarehouseNo())))
                .collect(Collectors.toList());
    }

    @Override
    public List<WarehouseStorageDTO> queryAllWarehouseList(WarehouseStorageQuery query) {
        //仓库信息
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryListWithBusinessWorkStandard(query);
        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageEntities.stream().map(WarehouseStorageCenterEntityConverter::entity2DTO).collect(Collectors.toList());

        List<Integer> adminId = warehouseStorageEntities.stream().map(WarehouseStorageEntity::getManageAdminId).collect(Collectors.toList());
        Map<Integer, String> adminId2RealNameMap = authQueryFacade.batchQueryAdmins(adminId);

        Map<Integer, WarehouseConfigCabinetStatusDTO> warehouseNo2CabinetStatusMap = wmsQueryFacade.queryAllWarehouseCabinetStatus();
        warehouseStorageDTOS.forEach(warehouse ->{
            WarehouseConfigCabinetStatusDTO warehouseConfigCabinetStatusDTO = warehouseNo2CabinetStatusMap.get(warehouse.getWarehouseNo());
            if(null != warehouseConfigCabinetStatusDTO){
                if (null != warehouseConfigCabinetStatusDTO.getCabinetStatus()) {
                    warehouse.setCabinetStatus(warehouseConfigCabinetStatusDTO.getCabinetStatus());
                }
                if (null != warehouseConfigCabinetStatusDTO.getExternalStatus()) {
                    warehouse.setExternalStatus(warehouseConfigCabinetStatusDTO.getExternalStatus());
                }
            }
            String realName = adminId2RealNameMap.get(warehouse.getManageAdminId());
            if(StringUtils.isNotBlank(realName)){
                warehouse.setManageAdminName(realName);
            }
        });
        return warehouseStorageDTOS;
    }

    @Override
    public PageInfo<WarehouseStorageDTO> queryBasePage(WarehouseStorageQuery query) {
        // 查询鲜沐自营仓配置
        List<Integer> selfWarehouseNos = wncConfig.querySelfWarehouseNos();
        if(query.getSelfWarehouseFlag() != null){
            if(query.getSelfWarehouseFlag()){// NOSONAR
                query.setSelfWarehouseNos(selfWarehouseNos);
            }else{
                query.setNoInSelfWarehouseNos(selfWarehouseNos);
            }
        }
        PageInfo<WarehouseStorageEntity> warehouseStorageEntityPageInfo = warehouseStorageCenterRepository.queryWarehouseStoragePage(query);

        PageInfo<WarehouseStorageDTO> warehouseStorageDTOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(warehouseStorageEntityPageInfo, warehouseStorageDTOPageInfo);
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageEntityPageInfo.getList();
        if(CollectionUtils.isNotEmpty(warehouseStorageEntities)){
            List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageEntities.stream()
                    .map(WarehouseStorageCenterEntityConverter::entity2DTO)
                    .peek(dto -> dto.setSelfWarehouseFlag(selfWarehouseNos.contains(dto.getWarehouseNo())))
                    .collect(Collectors.toList());

            warehouseStorageDTOPageInfo.setList(warehouseStorageDTOS);
        }

        return warehouseStorageDTOPageInfo;
    }

    @Override
    public List<Integer> queryWarehouseNosDataPermission(Integer adminId, Long tenantId) {
        if(adminId == null){
            throw new RuntimeException("登录人不能为空");
        }
        if(tenantId == null){
            throw new RuntimeException("租户ID不能为空");
        }
        List<Integer> warehouseNos = adminDataPermissionQueryFacade.queryWarehouseNosPermission(adminId);
        Optional<Integer> allWarehouseFlag = warehouseNos.stream().filter(warehouseNo -> Objects.equals(warehouseNo, 0)).findFirst();

        //全部仓库权限
        if(allWarehouseFlag.isPresent()){
            List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryWarehouseStorage(WarehouseStorageQuery.builder().tenantId(tenantId).build());
            if(CollectionUtils.isEmpty(warehouseStorageEntities)){
                return Collections.emptyList();
            }
            return warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList());
        }

        return warehouseNos;
    }
}
