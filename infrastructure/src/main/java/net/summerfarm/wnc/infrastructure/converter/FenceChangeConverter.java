package net.summerfarm.wnc.infrastructure.converter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.vo.FenceChangeRemarkVO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTask;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务转换器
 * date: 2023/8/25 16:15
 *
 * <AUTHOR>
 */
public class FenceChangeConverter {

    public static WncFenceChangeTask entity2Do(FenceChangeTaskEntity fenceChangeTaskEntity){
        if(fenceChangeTaskEntity == null){
            return null;
        }
        WncFenceChangeTask wncFenceChangeTask = new WncFenceChangeTask();
        wncFenceChangeTask.setId(fenceChangeTaskEntity.getId());
        if (fenceChangeTaskEntity.getType() != null){
            wncFenceChangeTask.setType(fenceChangeTaskEntity.getType().getValue());
        }
        if (fenceChangeTaskEntity.getStatus() != null){
            wncFenceChangeTask.setStatus(fenceChangeTaskEntity.getStatus().getValue());
        }
        wncFenceChangeTask.setAreaNo(fenceChangeTaskEntity.getAreaNo());
        wncFenceChangeTask.setStoreNo(fenceChangeTaskEntity.getStoreNo());
        wncFenceChangeTask.setFenceId(fenceChangeTaskEntity.getFenceId());
        wncFenceChangeTask.setFenceName(fenceChangeTaskEntity.getFenceName());
        wncFenceChangeTask.setTargetNo(fenceChangeTaskEntity.getTargetNo());
        if (!CollectionUtils.isEmpty(fenceChangeTaskEntity.getAdCodeMsgEntities())){
            String changeAcmIdStr = fenceChangeTaskEntity.getAdCodeMsgEntities().stream().map(AdCodeMsgEntity::getId).map(String::valueOf).collect(Collectors.joining(","));
            wncFenceChangeTask.setChangeAcmId(changeAcmIdStr);
        }
        wncFenceChangeTask.setExeTime(fenceChangeTaskEntity.getExeTime());
        wncFenceChangeTask.setCreateTime(fenceChangeTaskEntity.getCreateTime());
        wncFenceChangeTask.setUpdateTime(fenceChangeTaskEntity.getUpdateTime());
        wncFenceChangeTask.setCreatorId(fenceChangeTaskEntity.getCreatorId());
        if (fenceChangeTaskEntity.getFenceChangeRemarkVO() != null){
            wncFenceChangeTask.setRemark(JSON.toJSONString(fenceChangeTaskEntity.getFenceChangeRemarkVO()));
        }
        wncFenceChangeTask.setCreator(fenceChangeTaskEntity.getCreator());
        wncFenceChangeTask.setUpdater(fenceChangeTaskEntity.getUpdater());
        wncFenceChangeTask.setChangeAreaName(fenceChangeTaskEntity.getChangeAreaName());
        return wncFenceChangeTask;
    }

    public static FenceChangeTaskEntity do2Entity(WncFenceChangeTask wncFenceChangeTask){
        if(wncFenceChangeTask == null){
            return null;
        }
        FenceChangeTaskEntity fenceChangeTaskEntity = new FenceChangeTaskEntity();
        fenceChangeTaskEntity.setId(wncFenceChangeTask.getId());
        fenceChangeTaskEntity.setType(FenceChangeTaskEnums.Type.getTypeByValue(wncFenceChangeTask.getType()));
        fenceChangeTaskEntity.setStatus(FenceChangeTaskEnums.Status.getStatusByValue(wncFenceChangeTask.getStatus()));
        fenceChangeTaskEntity.setAreaNo(wncFenceChangeTask.getAreaNo());
        fenceChangeTaskEntity.setStoreNo(wncFenceChangeTask.getStoreNo());
        fenceChangeTaskEntity.setFenceId(wncFenceChangeTask.getFenceId());
        fenceChangeTaskEntity.setFenceName(wncFenceChangeTask.getFenceName());
        fenceChangeTaskEntity.setTargetNo(wncFenceChangeTask.getTargetNo());

        if(!StringUtils.isEmpty(wncFenceChangeTask.getChangeAcmId())){
            List<AdCodeMsgEntity> adCodeMsgEntities = Arrays.stream(wncFenceChangeTask.getChangeAcmId().split(",")).filter(StrUtil::isNotBlank).map(e -> {
                AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
                adCodeMsgEntity.setId(Integer.valueOf(e));
                return adCodeMsgEntity;
            }).collect(Collectors.toList());
            fenceChangeTaskEntity.setAdCodeMsgEntities(adCodeMsgEntities);
        }

        if(!StringUtils.isEmpty(wncFenceChangeTask.getRemark())){
            fenceChangeTaskEntity.setFenceChangeRemarkVO(JSON.parseObject(wncFenceChangeTask.getRemark(), FenceChangeRemarkVO.class));
        }
        fenceChangeTaskEntity.setExeTime(wncFenceChangeTask.getExeTime());
        fenceChangeTaskEntity.setCreateTime(wncFenceChangeTask.getCreateTime());
        fenceChangeTaskEntity.setUpdateTime(wncFenceChangeTask.getUpdateTime());
        fenceChangeTaskEntity.setCreatorId(wncFenceChangeTask.getCreatorId());
        fenceChangeTaskEntity.setCreator(wncFenceChangeTask.getCreator());
        fenceChangeTaskEntity.setUpdater(wncFenceChangeTask.getUpdater());
        fenceChangeTaskEntity.setChangeAreaName(wncFenceChangeTask.getChangeAreaName());
        return fenceChangeTaskEntity;
    }
}
