package net.summerfarm.wnc.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 围栏切仓任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 15:25:57
 */
@Getter
@Setter
@TableName("wnc_fence_change_task")
public class WncFenceChangeTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作围栏ID
     */
    @TableField("fence_id")
    private Integer fenceId;

    /**
     * 围栏名称
     */
    @TableField("fence_name")
    private String fenceName;

    /**
     * 操作围栏归属运营服务区编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 操作围栏归属城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 区域ID，多个区域 ','分割
     */
    @TableField("change_acm_id")
    private String changeAcmId;

    /**
     * 类型，0：切仓，1：切围栏， 2：自定义围栏
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 目标围栏ID/目标城配仓编号
     */
    @TableField("target_no")
    private Integer targetNo;

    /**
     * 状态，0：待处理，1：已取消，2：已完成，10：区域切换中，15：订单切换中，20：处理失败
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 执行时间
     */
    @TableField("exe_time")
    private LocalDateTime exeTime;

    /**
     * 切仓说明，json格式存储网点变更信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private Integer creatorId;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 切仓区域名称
     */
    @TableField("change_area_name")
    private String changeAreaName;

    /**
     * 切仓城市名称
     */
    @TableField("change_city_name")
    private String changeCityName;
}
