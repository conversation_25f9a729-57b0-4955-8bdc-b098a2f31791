package net.summerfarm.wnc.domain.changeTask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskDetailQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;

import java.util.Collection;
import java.util.List;

/**
 * Description:围栏切仓任务明细仓库接口
 * date: 2023/8/24 16:44
 *
 * <AUTHOR>
 */
public interface FenceChangeTaskDetailRepository {

    /**
     * 分页查询切仓任务订单列表
     * @param fenceChangeTaskOrderPageQuery 查询
     * @return 结果
     */
    PageInfo<FenceChangeTaskOrderEntity> queryTaskOrderPage(FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery);

    /**
     * 批量插入切仓任务订单明细
     * @param fenceChangeTaskOrderEntityList 切仓任务订单明细实体集合
     */
    void saveBatch(Collection<FenceChangeTaskOrderEntity> fenceChangeTaskOrderEntityList);

    /**
     * 更新切仓任务订单明细
     * @param fenceChangeTaskOrderEntity 切仓任务订单明细实体
     * @return 影响行数
     */
    int update(FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity);

    /**
     * 查询切仓任务订单明细
     * @param changeTaskDetailId 切仓任务订单明细ID
     * @return 切仓任务订单明细
     */
    FenceChangeTaskOrderEntity queryById(Long changeTaskDetailId);

    /**
     * 查询可重试切仓任务订单明细
     * @return 切仓任务订单明细集合
     */
    List<FenceChangeTaskOrderEntity> queryRetryableTaskDetails();

    /**
     * 查询切仓任务订单明细集合
     * @param fenceChangeTaskDetailQuery 查询
     * @return 结果
     */
    List<FenceChangeTaskOrderEntity> queryList(FenceChangeTaskDetailQuery fenceChangeTaskDetailQuery);
}
