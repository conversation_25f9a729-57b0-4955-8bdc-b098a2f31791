package net.summerfarm.wnc.domain.preciseDelivery.aggregate;

import lombok.Data;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigAreaValueObject;
import net.summerfarm.wnc.domain.preciseDelivery.valueObject.PreciseDeliveryConfigTimeValueObject;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:精准送配置聚合根
 * date: 2024/1/22 14:50
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigAggregate implements Serializable {

    private static final long serialVersionUID = 762296124470437302L;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 精准送配置区域集合
     */
    private List<PreciseDeliveryConfigAreaValueObject> areaList;

    /**
     * 精准送配置时效集合
     */
    private List<PreciseDeliveryConfigTimeValueObject> timeList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
