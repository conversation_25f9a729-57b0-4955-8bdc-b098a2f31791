package net.summerfarm.wnc.domain.deliveryRule;

import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;
import net.summerfarm.wnc.domain.config.monit.PopMerchantMonit;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/11/13 11:39<br/>
 *
 * <AUTHOR> />
 */
public interface ContactDeliveryRuleRepository {

    /**
     * 根据唯一键查询地址配送规则
     * @param outBusinessNo 外部联系人编号
     * @param systemSource 系统来源
     * @return 结果
     */
    ContactDeliveryRuleEntity queryByUk(String outBusinessNo,Integer systemSource);

    /**
     * 新增更新地址配送规则
     * @param entity 实体
     */
    void saveOrUpdate(ContactDeliveryRuleEntity entity);

    /**
     * 删除配送规则
     * @param outBusinessNo 外部联系人编号
     * @param systemSource 系统来源
     */
    void delteByUk(String outBusinessNo, Integer systemSource);

    /**
     * 查询地址配送规则列表
     * @param query 查询条件
     * @return 结果
     */
    List<ContactDeliveryRuleEntity> queryList(ContactDeliveryRuleQuery query);

    /**
     * 查询POP没有指定配送周期的数据
     * @param adminId 大客户ID
     * @return 结果
     */
    List<PopMerchantMonit> queryPopNoDeliveryRuleMonit(Integer adminId);
}
