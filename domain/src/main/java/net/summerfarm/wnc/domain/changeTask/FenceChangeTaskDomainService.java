package net.summerfarm.wnc.domain.changeTask;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeAreaHandleMsg;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeOrderHandleMsg;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.common.enums.WarehouseStorageCenterEnums;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskQuery;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.changeTask.vo.FenceChangeRemarkVO;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.AreaEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.facade.ofc.OfcCommandFacade;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentChangeCommandInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务领域服务
 * date: 2023/8/28 15:27
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceChangeTaskDomainService {

    private final FenceRepository fenceRepository;
    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    private final AreaRepository areaRepository;
    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    private final FenceChangeTaskRepository fenceChangeTaskRepository;
    private final FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    private final AdCodeMsgRepository adCodeMsgRepository;
    private final OfcCommandFacade ofcCommandFacade;
    private final MqProducer mqProducer;
    private final FenceChangeTaskSender fenceChangeTaskSender;
    private final FenceChangeTaskValidator fenceChangeTaskValidator;

    /**
     * 查询围栏切仓任务说明
     * @param fenceChangeTaskEntity 围栏切仓任务实体
     * @return 围栏切仓任务说明
     */
    public FenceChangeRemarkVO queryTaskChangeRemark(FenceChangeTaskEntity fenceChangeTaskEntity) {
        //查询当前围栏有效性
        FenceEntity currentFence = fenceRepository.queryById(fenceChangeTaskEntity.getFenceId());
        if (currentFence == null){
            throw new BizException("操作围栏不存在");
        }
        fenceChangeTaskEntity.setFenceName(currentFence.getFenceName());
        //查询当前围栏运营区域
        AreaEntity currentArea = areaRepository.queryByUk(currentFence.getAreaNo());
        if (currentArea == null){
            throw new BizException("操作围栏归属运营服务区域不存在");
        }
        fenceChangeTaskEntity.setAreaNo(currentFence.getAreaNo());
        //设置运营区域编号 todo 确认作用
        //查询当前围栏城配仓
        WarehouseLogisticsCenterEntity currentStore = warehouseLogisticsCenterRepository.queryByUk(currentFence.getStoreNo());
        if (currentStore == null){
            throw new BizException("操作围栏归属城配仓不存在");
        }
        fenceChangeTaskEntity.setStoreNo(currentStore.getStoreNo());
        //查询当前城配仓库存使用仓
        List<WarehouseStorageEntity> oldWarehouseStorageEntities = warehouseStorageCenterRepository.queryListByStoreNo(currentFence.getStoreNo());
        List<String> oldWarehouses = oldWarehouseStorageEntities.stream().filter(e -> Objects.equals(e.getStatus(), WarehouseStorageCenterEnums.Status.OPEN.getValue()))
                .map(WarehouseStorageEntity::getWarehouseName).sorted().collect(Collectors.toList());

        FenceChangeRemarkVO fenceChangeRemarkVO = new FenceChangeRemarkVO();
        fenceChangeRemarkVO.init(currentArea.getAreaNo(), currentArea.getAreaName(), currentStore.getStoreName(), oldWarehouses, fenceChangeTaskEntity.getType());
        fenceChangeRemarkVO.setOldAreaNo(currentFence.getAreaNo());
        fenceChangeRemarkVO.setOldStoreNo(currentFence.getStoreNo());
        //校验目标围栏ID/目标城配仓编号有效性
        if (fenceChangeTaskEntity.isFenceType()){
            //围栏切仓类型
            FenceEntity targetFence = fenceRepository.queryById(fenceChangeTaskEntity.getTargetNo());
            if (targetFence == null){
                throw new BizException("目标围栏不存在");
            }
            if (Objects.equals(currentFence.getId(), targetFence.getId())){
                throw new BizException("目标围栏与操作围栏一致,无法切换");
            }
            //查询目标围栏运营区域
            AreaEntity targetArea = areaRepository.queryByUk(targetFence.getAreaNo());
            if (targetArea == null){
                throw new BizException("目标围栏归属运营服务区域不存在");
            }
            fenceChangeRemarkVO.setNewArea(targetArea.getAreaName());
            fenceChangeRemarkVO.setNewAreaNo(targetArea.getAreaNo());
            //查询目标围栏城配仓
            WarehouseLogisticsCenterEntity targetStore = warehouseLogisticsCenterRepository.queryByUk(targetFence.getStoreNo());
            if (targetStore == null){
                throw new BizException("目标围栏归属城配仓不存在");
            }
            fenceChangeRemarkVO.setNewStore(targetStore.getStoreName());
            fenceChangeRemarkVO.setNewStoreNo(targetStore.getStoreNo());
        }else {
            //城配切仓类型
            //查询目标围栏城配仓
            WarehouseLogisticsCenterEntity targetStore = warehouseLogisticsCenterRepository.queryByUk(fenceChangeTaskEntity.getTargetNo());
            if (targetStore == null){
                throw new BizException("目标城配仓不存在");
            }
            if (Objects.equals(currentFence.getStoreNo(), targetStore.getStoreNo())){
                throw new BizException("目标城配仓与操作围栏归属城配仓一致,无法切换");
            }
            fenceChangeRemarkVO.setNewStore(targetStore.getStoreName());
            fenceChangeRemarkVO.setNewStoreNo(targetStore.getStoreNo());
        }
        List<String> newWarehouses = fenceChangeRemarkVO.getOldWarehouses();
        //判断城配仓是否发生变化
        if (fenceChangeRemarkVO.isStoreChange()){
            //查询目标城配仓库存使用仓
            List<WarehouseStorageEntity> newWarehouseStorageEntities = warehouseStorageCenterRepository.queryListByStoreNo(fenceChangeRemarkVO.getNewStoreNo());
            newWarehouses = newWarehouseStorageEntities.stream().filter(e -> Objects.equals(e.getStatus(), WarehouseStorageCenterEnums.Status.OPEN.getValue()))
                    .map(WarehouseStorageEntity::getWarehouseName).sorted().collect(Collectors.toList());
        }
        fenceChangeRemarkVO.setNewWarehouses(newWarehouses);
        //查询当前围栏最近配送日期(yyyy-MM-dd) + 城配仓截单时间(HH:mm:ss) + 半小时
        fenceChangeRemarkVO.setExeTime(this.getFenceChangeExcTime(fenceChangeTaskEntity, currentStore.getCloseTime()));
        return fenceChangeRemarkVO;
    }

    public LocalDateTime getFenceChangeExcTime(FenceChangeTaskEntity fenceChangeTaskEntity, String closeTimeStr) {
        if (StrUtil.isBlank(closeTimeStr)){
            throw new BizException("操作围栏归属城配仓截单时间未配置");
        }
        LocalTime closeTime = LocalTime.parse(closeTimeStr);
        //计算日期
        LocalDate calcDate = LocalTime.now().isBefore(closeTime) ? LocalDate.now().plusDays(1) : LocalDate.now().plusDays(2);
        FenceDeliveryEntity fenceDeliveryEntity = fenceRepository.queryDeliveryById(fenceChangeTaskEntity.getFenceId());
        if (fenceDeliveryEntity == null || fenceDeliveryEntity.getNextDeliveryDate() == null){
            throw new BizException("操作围栏配送周期或首配日未配置");
        }
        if (fenceDeliveryEntity.getNextDeliveryDate().isAfter(calcDate)){
            //首配日比计算日期还晚的情况 计算日期调整为首配日
            calcDate = fenceDeliveryEntity.getNextDeliveryDate();
        }
        LocalDate exeDate = calcDate;
        FenceDeliveryEnums.FrequentMethod method = FenceDeliveryEnums.FrequentMethod.getMethodByValue(fenceDeliveryEntity.getFrequentMethod());
        //周计算
        if(Objects.equals(method, FenceDeliveryEnums.FrequentMethod.WEEK_CALC)){
            String[] split = fenceDeliveryEntity.getDeliveryFrequent().split(",");
            Integer[] dfArr = Convert.toIntArray(split);
            Arrays.sort(dfArr);
            if (dfArr.length == 0){
                throw new BizException("操作围栏配送周期中周计算方案未配置");
            }
            if(Objects.equals(dfArr[0], 0)){
                exeDate = calcDate;
            } else {
                int curWeek = calcDate.getDayOfWeek().getValue();
                int firstWeek = dfArr[0];
                int lastWeek = dfArr[dfArr.length - 1];
                if(lastWeek < curWeek){
                    exeDate = calcDate.plusDays(7 + firstWeek - curWeek);
                } else {
                    for (Integer calcWeek : dfArr) {
                        if (calcWeek >= curWeek) {
                            exeDate = calcDate.plusDays(calcWeek - curWeek);
                            break;
                        }
                    }
                }
            }
        }else {
            //开始计算日期
            LocalDate beginCalculateDate = fenceDeliveryEntity.getBeginCalculateDate();
            //配送间隔周期
            Integer deliveryFrequentInterval = fenceDeliveryEntity.getDeliveryFrequentInterval();
            //相等
            if(Objects.equals(exeDate, beginCalculateDate)){
                exeDate = beginCalculateDate;
            }else{
                //开始计算时间在之前，业务也只会在当前以及以前
                LocalDate nextDate = beginCalculateDate.plusDays(deliveryFrequentInterval);
                while(!nextDate.isAfter(exeDate) && !exeDate.isEqual(nextDate)){
                    nextDate = nextDate.plusDays(deliveryFrequentInterval);
                }
                exeDate = nextDate;
            }
        }

        //当前围栏最近配送日期前一天 考虑运营服务区加单 默认+30min
        return LocalDateTime.of(exeDate.minusDays(1), closeTime.plusMinutes(30));
    }

    @Transactional(rollbackFor = Exception.class)
    public void doFenceChangeAreaHandle(FenceChangeTaskEntity task) {
        if (task == null || task.getFenceChangeRemarkVO() == null || task.getTargetNo() == null){
            throw new BizException("异常数据导致执行区域切换异常");
        }
        Integer targetNo = task.getTargetNo();
        if (task.isFenceType()){
            FenceEntity targetFence = fenceRepository.queryById(targetNo);
            if(targetFence == null){
                throw new BizException("目标围栏不存在,执行区域切换异常");
            }
            //围栏切仓类型
            List<AdCodeMsgEntity> adCodeMsgEntities = task.getAdCodeMsgEntities();
            List<AdCodeMsgEntity> updateList = adCodeMsgEntities.stream().map(e -> {
                AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
                adCodeMsgEntity.setId(e.getId());
                adCodeMsgEntity.setFenceId(targetNo);
                adCodeMsgEntity.setStatus(targetFence.getStatus().getValue());
                adCodeMsgEntity.setUpdateTime(LocalDateTime.now());
                return adCodeMsgEntity;
            }).collect(Collectors.toList());
            updateList.stream().sorted(Comparator.comparing(AdCodeMsgEntity::getId)).forEach(adCodeMsgRepository::update);
        }else {
            //城配切仓类型
            FenceEntity fenceEntity = new FenceEntity();
            fenceEntity.setId(task.getFenceId());
            fenceEntity.setStoreNo(targetNo);
            fenceEntity.setUpdateTime(LocalDateTime.now());
            fenceRepository.update(fenceEntity);
        }
        //城配仓、运营区域都未发生变化 无需处理联系人以及客户相关信息
        FenceChangeRemarkVO fenceChangeRemarkVO = task.getFenceChangeRemarkVO();
        if (!fenceChangeRemarkVO.isAreaAndStoreChange()){
            FenceChangeTaskEntity updateComplete = task.execute(FenceChangeTaskEnums.Status.COMPLETED);
            fenceChangeTaskRepository.update(updateComplete);
            return;
        }
        FenceChangeTaskEntity updateOrderIng = task.execute(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING);
        fenceChangeTaskRepository.update(updateOrderIng);

        //构建围栏切仓相关变更数据
        FenceChangeAreaHandleMsg fenceChangeAreaHandleMsg = task.buildMqMsg();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_FENCE, WncMqConstants.Tag.TAG_FENCE_CHANGE_AREA_HANDLE, fenceChangeAreaHandleMsg);
                } catch (Throwable e) {
                    log.error("围栏切仓任务编号：{}区域切换处理发送消息失败，异常原因：{}", task.getId(), e.getMessage(), e);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void doFenceChangeOrderHandle(FenceChangeTaskEntity waitHandleTask, FenceChangeTaskOrderEntity waitHandleTaskOrder) {
        FenceChangeRemarkVO fenceChangeRemarkVO = waitHandleTask.getFenceChangeRemarkVO();
        FulfillmentChangeCommandInput commandInput = FulfillmentChangeCommandInput.builder()
                .outerOrderId(waitHandleTaskOrder.getOuterOrderId())
                .source(waitHandleTaskOrder.getSource().getValue())
                .deliveryTime(waitHandleTaskOrder.getDeliveryTime())
                .outerContactId(waitHandleTaskOrder.getOuterContactId())
                .oldStoreNo(fenceChangeRemarkVO.getOldStoreNo())
                .newStoreNo(fenceChangeRemarkVO.getNewStoreNo())
                .creator(waitHandleTask.getCreator())
                .build();
        if (fenceChangeRemarkVO.isStoreChange()){
            //更新履约单履约仓
            ofcCommandFacade.updateStoreNo(commandInput);
        }
        FenceChangeTaskOrderEntity updateOrderSuccess = waitHandleTaskOrder.execute(FenceChangeTaskDetailEnums.Status.SUCCESS, null);
        fenceChangeTaskDetailRepository.update(updateOrderSuccess);
        if (!fenceChangeRemarkVO.isAreaChange()){
            return;
        }
        //更新订单运营服务区域
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    //构建围栏切仓相关变更数据
                    FenceChangeOrderHandleMsg fenceChangeOrderHandleMsg = waitHandleTaskOrder.buildMqMsg(fenceChangeRemarkVO);
                    mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_FENCE, WncMqConstants.Tag.TAG_FENCE_CHANGE_ORDER_HANDLE, fenceChangeOrderHandleMsg);
                } catch (Throwable e) {
                    log.error("围栏切仓任务编号：{}，订单标识：{}，订单切换处理发送消息失败，异常原因：{}", waitHandleTask.getId(), waitHandleTaskOrder.buildUk(), e.getMessage(), e);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBatchDetail(Collection<FenceChangeTaskOrderEntity> fenceChangeTaskOrderEntities) {
        if (CollectionUtils.isEmpty(fenceChangeTaskOrderEntities)){
            return;
        }
        fenceChangeTaskDetailRepository.saveBatch(fenceChangeTaskOrderEntities);
    }

    /**
     * 查询围栏是否处于预约切仓状态
     * @param fenceId 围栏ID
     */
    public List<FenceChangeTaskEntity> queryExecutingFenceChangeTasks(Integer fenceId) {
        if (fenceId == null){
            return Collections.emptyList();
        }
        List<FenceChangeTaskEntity> executingFenceChangeTasks = fenceChangeTaskRepository.queryListWithArea(FenceChangeTaskQuery.builder()
                .fenceId(fenceId)
                .status(FenceChangeTaskEnums.Status.getExecutingStatusCode()).build());
        return executingFenceChangeTasks;
    }

    /**
     * 创建切仓任务
     * @param fenceChangeTaskEntity 切仓任务实体
     */
    public Long createFenceChangeTask(FenceChangeTaskEntity fenceChangeTaskEntity) {
        if (FenceChangeTaskEnums.Type.CUSTOM.equals(fenceChangeTaskEntity.getType())) {


        } else {
            // 普通围栏切仓相关校验
            fenceChangeTaskValidator.validateFenceChangeTask(fenceChangeTaskEntity);
            // 查询围栏切仓任务说明
            FenceChangeRemarkVO fenceChangeRemarkVO = this.queryTaskChangeRemark(fenceChangeTaskEntity);
            fenceChangeTaskEntity.resetFenceChangeRemark(fenceChangeRemarkVO);
        }

        Long fenceChangeTaskId = fenceChangeTaskRepository.save(fenceChangeTaskEntity);
        // 发送飞书通知
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    fenceChangeTaskSender.sendCreateMsg(fenceChangeTaskEntity);
                } catch (Throwable e) {
                    log.error("发送创建切仓消息异常:{}", JSONObject.toJSONString(fenceChangeTaskEntity), e);
                }
            }
        });
        return fenceChangeTaskId;
    }

    /**
     * 取消切仓任务
     * @param fenceChangeTaskEntity 切仓任务实体
     */
    public void cancelFenceChangeTask(FenceChangeTaskEntity fenceChangeTaskEntity) {
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        FenceChangeTaskEntity updateEntity = fenceChangeTaskEntity.cancel(user.getNickname(), user.getBizUserId());
        fenceChangeTaskRepository.update(updateEntity);

        // 发送飞书通知
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    //飞书消息通知
                    fenceChangeTaskSender.sendCancelMsg(fenceChangeTaskEntity);
                } catch (Throwable e) {
                    log.error("发送取消切仓消息异常:{}", JSONObject.toJSONString(fenceChangeTaskEntity), e);
                }
            }
        });
    }

    /**
     * 更新切仓任务状态
     * @param fenceChangeTaskIds 切仓任务ID集合
     * @param sourceStatus 源状态
     * @param targetStatus 目标状态
     */
    public void updateFenceChangeTaskStatus(List<Long> fenceChangeTaskIds, Integer sourceStatus, Integer targetStatus) {
        if (CollectionUtils.isEmpty(fenceChangeTaskIds) || sourceStatus == null || targetStatus == null){
            return;
        }
        fenceChangeTaskRepository.updateTargetStatusByIds(fenceChangeTaskIds,sourceStatus,targetStatus);
    }
}
