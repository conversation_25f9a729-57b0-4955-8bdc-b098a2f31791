package net.summerfarm.wnc.domain.preciseDelivery.param;

import lombok.Data;
import net.summerfarm.wnc.common.constants.AppConsts;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:精准送配置区域操作参数
 * date: 2024/1/22 15:09
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigAreaCommandParam implements Serializable {

    private static final long serialVersionUID = 2836600744121725552L;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public String buildUk(){
        String area = this.area == null ? "" : this.area;
        return this.city + AppConsts.Symbol.HASH_TAG + area;
    }
}
