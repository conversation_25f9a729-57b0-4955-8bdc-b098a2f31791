package net.summerfarm.wnc.domain.changeTask;

import lombok.RequiredArgsConstructor;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务校验器
 * date: 2023/8/28 15:52
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class FenceChangeTaskValidator {

    private final FenceRepository fenceRepository;
    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    private final AdCodeMsgRepository adCodeMsgRepository;
    private final FenceChangeTaskRepository fenceChangeTaskRepository;

    /**
     * 预约围栏切仓任务校验
     * @param fenceChangeTaskEntity 围栏切仓任务实体
     */
    public void validateFenceChangeTask(FenceChangeTaskEntity fenceChangeTaskEntity){
        //校验目标围栏ID/目标城配仓编号有效性
        List<Integer> fenceAreaIds = fenceChangeTaskEntity.getAdCodeMsgEntities().stream().map(AdCodeMsgEntity::getId).collect(Collectors.toList());
        //校验围栏划分行政区域有效性以及是否正在切仓
        List<AdCodeMsgEntity> currentFenceAreas = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder().areaIds(fenceAreaIds).build());
        if (!Objects.equals(fenceAreaIds.size(), currentFenceAreas.size())){
            throw new BizException("存在无效切仓区域");
        }
        //设置切仓区域
        fenceChangeTaskEntity.setAdCodeMsgEntities(currentFenceAreas);
        //查询出待处理、区域切换中围栏切仓任务
        List<FenceChangeTaskEntity> allFenceChangeTasks = fenceChangeTaskRepository.queryListWithArea(FenceChangeTaskQuery.builder().status(Arrays.asList(FenceChangeTaskEnums.Status.WAIT.getValue(), FenceChangeTaskEnums.Status.AREA_CHANGE_ING.getValue())).build());
        Set<Integer> fenceChangingAreaIds = allFenceChangeTasks.stream().map(FenceChangeTaskEntity::getAdCodeMsgEntities).flatMap(Collection::stream).map(AdCodeMsgEntity::getId).collect(Collectors.toSet());
        Optional<Integer> areaValid = fenceAreaIds.stream().filter(fenceChangingAreaIds::contains).findFirst();
        if (areaValid.isPresent()){
            throw new BizException("存在待处理或区域切换中的切仓区域");
        }
        //校验目标围栏ID/目标城配仓编号有效性
        if (fenceChangeTaskEntity.isFenceType()){
            //围栏切仓类型
            FenceEntity targetFence = fenceRepository.queryById(fenceChangeTaskEntity.getTargetNo());
            if (targetFence == null){
                throw new BizException("目标围栏不存在");
            }
            //目标围栏存在切仓类型==“城配仓”&状态==“待处理、区域切换中”的切仓任务
            Optional<FenceChangeTaskEntity> fenceValid = allFenceChangeTasks.stream().filter(e -> Objects.equals(e.getFenceId(), targetFence.getId()) && !e.isFenceType()).findFirst();
            if (fenceValid.isPresent()){
                throw new BizException("目标围栏存在待处理的“城配仓”类型切仓任务");
            }
            //目标围栏与当前围栏行政城市不同
            List<String> currentCityList = currentFenceAreas.stream().map(AdCodeMsgEntity::getCity).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentCityList) || currentCityList.size() > 1){
                throw new BizException("本次切仓涉及的围栏区域行政城市不同");
            }
            //查询目标围栏划分行政区
            String targetCity = currentCityList.get(0);
            List<AdCodeMsgEntity> targetFenceAreas = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder()
                    .fenceId(targetFence.getId())
                    .statusList(AdCodeMsgEnums.Status.getUsableStatus()).build());
            if (CollectionUtils.isEmpty(targetFenceAreas)){
                //获取目标围栏是否已经存在切仓类型==“围栏”&状态==“待处理、区域切换中”的切仓任务
                Optional<AdCodeMsgEntity> firstFenceAreas = allFenceChangeTasks.stream().filter(e -> Objects.equals(e.getTargetNo(), targetFence.getId()) && e.isFenceType())
                        .map(FenceChangeTaskEntity::getAdCodeMsgEntities).flatMap(Collection::stream).findFirst();
                if (firstFenceAreas.isPresent()){
                    targetCity = firstFenceAreas.get().getCity();
                }
            }else {
                targetCity = targetFenceAreas.get(0).getCity();
            }
            if (!Objects.equals(currentCityList.get(0), targetCity)){
                throw new BizException("围栏区域与目标所属的行政城市不同");
            }
        }else {
            //城配切仓类型
            //查询目标围栏城配仓
            WarehouseLogisticsCenterEntity targetStore = warehouseLogisticsCenterRepository.queryByUk(fenceChangeTaskEntity.getTargetNo());
            if (targetStore == null){
                throw new BizException("目标城配仓不存在");
            }
            //目标城配仓不存在围栏
            List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().storeNo(targetStore.getStoreNo()).build());
            if (CollectionUtils.isEmpty(fenceEntities)){
                throw new BizException("目标城配仓的围栏不存在");
            }
        }
    }
}
