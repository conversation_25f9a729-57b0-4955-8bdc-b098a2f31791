package net.summerfarm.wnc.domain.preciseDelivery.param;

import cn.hutool.core.date.TimeInterval;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Description:精准送配置时效操作参数
 * date: 2024/1/22 15:11
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigTimeCommandParam implements Serializable {

    private static final long serialVersionUID = 5404992849330255634L;

    /**
     * 开始时间
     */
    private LocalTime beginTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public boolean isEndAfterBegin() {
        return this.endTime.isAfter(this.beginTime);
    }
}
