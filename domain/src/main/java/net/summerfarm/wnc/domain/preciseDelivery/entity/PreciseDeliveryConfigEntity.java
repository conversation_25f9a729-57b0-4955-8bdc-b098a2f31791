package net.summerfarm.wnc.domain.preciseDelivery.entity;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:精准送配置实体
 * date: 2024/1/22 13:54
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigEntity implements Serializable {

    private static final long serialVersionUID = 7014187829432903210L;

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
