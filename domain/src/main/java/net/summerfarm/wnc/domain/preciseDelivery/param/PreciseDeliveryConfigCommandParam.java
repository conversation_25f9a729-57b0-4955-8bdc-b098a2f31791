package net.summerfarm.wnc.domain.preciseDelivery.param;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: 精准送配置操作参数
 * date: 2024/1/22 11:23
 *
 * <AUTHOR>
 */
@Data
public class PreciseDeliveryConfigCommandParam implements Serializable {

    private static final long serialVersionUID = -1179637949477148571L;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 精准送配置区域集合
     */
    private List<PreciseDeliveryConfigAreaCommandParam> areaList;

    /**
     * 精准送配置时效集合
     */
    private List<PreciseDeliveryConfigTimeCommandParam> timeList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public void create(String operator) {
        this.creator = operator;
        this.createTime = LocalDateTime.now();
    }

    public void update(String operator) {
        this.updater = operator;
        this.updateTime = LocalDateTime.now();
    }
}
