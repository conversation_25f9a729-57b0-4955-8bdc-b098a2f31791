package net.summerfarm.wnc.domain.preciseDelivery.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * Description:精准送配置查询参数
 * date: 2024/1/22 15:07
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreciseDeliveryConfigQueryParam extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 3611718911074528841L;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;
}
