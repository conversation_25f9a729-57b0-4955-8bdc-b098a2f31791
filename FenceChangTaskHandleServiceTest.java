package net.summerfarm.wnc.application.service.changeTask;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * FenceChangTaskHandleService 测试类
 * 主要测试优化后的 executeCustomFenceChangeAreaHandleByBatch 方法
 */
public class FenceChangTaskHandleServiceTest {

    @InjectMocks
    private FenceChangTaskHandleService fenceChangTaskHandleService;

    @Mock
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testExecuteCustomFenceChangeAreaHandleByBatch_EmptyFenceChangeRecords() {
        // Given
        String changeBatchNo = "BATCH_001";
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();
        
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo))
                .thenReturn(Collections.emptyList());

        // When
        fenceChangTaskHandleService.executeCustomFenceChangeAreaHandleByBatch(changeBatchNo, records);

        // Then
        verify(wncFenceChangeRecordsQueryRepository).selectWithAreaByChangeBatchNo(changeBatchNo);
        // 验证当没有围栏变更记录时，方法能正常返回而不抛出异常
    }

    @Test
    void testExecuteCustomFenceChangeAreaHandleByBatch_EmptyAfterRecords() {
        // Given
        String changeBatchNo = "BATCH_002";
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = createMockBeforeFenceChangeRecords();
        
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo))
                .thenReturn(fenceChangeRecords);

        // When
        fenceChangTaskHandleService.executeCustomFenceChangeAreaHandleByBatch(changeBatchNo, records);

        // Then
        verify(wncFenceChangeRecordsQueryRepository).selectWithAreaByChangeBatchNo(changeBatchNo);
        // 验证当没有AFTER阶段的记录时，方法能正常返回
    }

    @Test
    void testBuildCustomFenceChangeContext() throws Exception {
        // Given
        String changeBatchNo = "BATCH_003";
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = createMockFenceChangeRecords();
        
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo))
                .thenReturn(fenceChangeRecords);

        // When - 使用反射调用私有方法进行测试
        Object context = ReflectionTestUtils.invokeMethod(
                fenceChangTaskHandleService, 
                "buildCustomFenceChangeContext", 
                changeBatchNo, 
                records
        );

        // Then
        // 验证上下文构建成功
        assert context != null;
        verify(wncFenceChangeRecordsQueryRepository).selectWithAreaByChangeBatchNo(changeBatchNo);
    }

    @Test
    void testDetermineFenceChangeType_NoneToCustom() throws Exception {
        // Given
        List<WncFenceChangeRecordsEntity> emptyBeforeRecords = Collections.emptyList();

        // When - 使用反射调用私有方法
        Object changeType = ReflectionTestUtils.invokeMethod(
                fenceChangTaskHandleService,
                "determineFenceChangeType",
                emptyBeforeRecords
        );

        // Then
        // 验证返回的是 NONE_TO_CUSTOM 类型
        assert changeType != null;
        assert changeType.toString().equals("NONE_TO_CUSTOM");
    }

    /**
     * 创建模拟的城市区域变更记录
     */
    private List<WncCityAreaChangeWarehouseRecordsEntity> createMockRecords() {
        WncCityAreaChangeWarehouseRecordsEntity record = new WncCityAreaChangeWarehouseRecordsEntity();
        record.setId(1L);
        record.setProvince("广东省");
        record.setCity("深圳市");
        record.setArea("南山区");
        record.setChangeBatchNo("BATCH_001");
        record.setFenceChangeTaskId(100L);
        
        return Arrays.asList(record);
    }

    /**
     * 创建模拟的围栏变更记录（仅BEFORE阶段）
     */
    private List<WncFenceChangeRecordsEntity> createMockBeforeFenceChangeRecords() {
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceChangeStage("BEFORE");
        beforeRecord.setFenceId(1001);
        
        return Arrays.asList(beforeRecord);
    }

    /**
     * 创建模拟的围栏变更记录（包含BEFORE和AFTER阶段）
     */
    private List<WncFenceChangeRecordsEntity> createMockFenceChangeRecords() {
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceChangeStage("BEFORE");
        beforeRecord.setFenceId(1001);
        
        WncFenceChangeRecordsEntity afterRecord = new WncFenceChangeRecordsEntity();
        afterRecord.setId(2L);
        afterRecord.setFenceChangeStage("AFTER");
        afterRecord.setFenceId(1002);
        
        return Arrays.asList(beforeRecord, afterRecord);
    }
}
