# executeCustomFenceChangeAreaHandleByBatch 方法优化说明

## 优化目标
重构 `executeCustomFenceChangeAreaHandleByBatch` 方法，提高代码的可读性、可维护性和可测试性，消除重复代码和深层嵌套。

## 优化前的问题

### 1. 代码结构问题
- **深层嵌套**：多层 if-else 嵌套，代码可读性差
- **重复代码**：三种处理场景中有大量重复的代码块
- **方法过长**：单个方法承担了太多职责，违反单一职责原则
- **硬编码逻辑**：围栏类型判断逻辑直接写在主方法中

### 2. 维护性问题
- **难以扩展**：新增围栏变更类型需要修改主方法
- **测试困难**：无法单独测试各种变更场景
- **错误定位**：出现问题时难以快速定位到具体的处理逻辑

## 优化方案

### 1. 引入上下文模式（Context Pattern）
创建 `CustomFenceChangeContext` 上下文类，封装所有相关的数据和状态：

```java
@Data
@Builder
private static class CustomFenceChangeContext {
    private String changeBatchNo;
    private List<WncCityAreaChangeWarehouseRecordsEntity> records;
    private String province;
    private String city;
    private String area;
    private List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords;
    private List<WncFenceChangeRecordsEntity> afterFenceChangeRecords;
}
```

### 2. 引入策略模式（Strategy Pattern）
定义 `FenceChangeType` 枚举，明确各种围栏变更类型：

```java
private enum FenceChangeType {
    CUSTOM_TO_CUSTOM,    // 自定义围栏 -> 自定义围栏
    NORMAL_TO_CUSTOM,    // 普通围栏 -> 自定义围栏
    NONE_TO_CUSTOM,      // 无围栏 -> 自定义围栏
    UNKNOWN              // 未知类型
}
```

### 3. 方法职责分离
将原来的大方法拆分为多个职责单一的小方法：

- `buildCustomFenceChangeContext()` - 构建上下文
- `determineFenceChangeType()` - 确定变更类型
- `executeCustomFenceChangeByType()` - 根据类型执行处理
- `handleCustomToCustomFenceChange()` - 处理自定义到自定义
- `handleNormalToCustomFenceChange()` - 处理普通到自定义
- `handleNoneToCustomFenceChange()` - 处理无到自定义
- `sendCustomFenceChangeMessage()` - 发送消息

## 优化后的代码结构

### 主方法简化
```java
@Transactional(rollbackFor = Exception.class)
public void executeCustomFenceChangeAreaHandleByBatch(String changeBatchNo, List<WncCityAreaChangeWarehouseRecordsEntity> records) {
    try {
        log.info("开始处理自定义围栏切仓区域任务，changeBatchNo: {}, 记录数: {}", changeBatchNo, records.size());
        
        // 1. 参数验证和基础信息提取
        CustomFenceChangeContext context = buildCustomFenceChangeContext(changeBatchNo, records);
        if (context == null) {
            return;
        }

        // 2. 根据围栏变更类型执行相应的处理逻辑
        FenceChangeType changeType = determineFenceChangeType(context.getBeforeFenceChangeRecords());
        executeCustomFenceChangeByType(changeType, context);

        log.info("完成处理自定义围栏切仓区域任务，changeBatchNo: {}", changeBatchNo);
    } catch (Exception e) {
        log.error("处理自定义围栏切仓区域任务失败，changeBatchNo: {}, 错误信息: {}", changeBatchNo, e.getMessage(), e);
        throw e;
    }
}
```

### 类型判断逻辑提取
```java
private FenceChangeType determineFenceChangeType(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
    if (CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
        return FenceChangeType.NONE_TO_CUSTOM;
    }

    List<FenceEnums.Type> types = beforeFenceChangeRecords.stream()
            .map(WncFenceChangeRecordsEntity::getFenceDetailEntity)
            .map(FenceEntity::getType)
            .distinct()
            .collect(Collectors.toList());

    if (types.size() == 1 && types.contains(FenceEnums.Type.CUSTOM)) {
        return FenceChangeType.CUSTOM_TO_CUSTOM;
    } else if (types.size() > 1 && types.contains(FenceEnums.Type.CUSTOM)) {
        return FenceChangeType.NORMAL_TO_CUSTOM;
    } else {
        return FenceChangeType.UNKNOWN;
    }
}
```

### 策略执行逻辑
```java
private void executeCustomFenceChangeByType(FenceChangeType changeType, CustomFenceChangeContext context) {
    switch (changeType) {
        case CUSTOM_TO_CUSTOM:
            handleCustomToCustomFenceChange(context);
            break;
        case NORMAL_TO_CUSTOM:
            handleNormalToCustomFenceChange(context);
            break;
        case NONE_TO_CUSTOM:
            handleNoneToCustomFenceChange(context);
            break;
        default:
            log.warn("围栏切仓区域处理任务，围栏类型异常，changeBatchNo: {}", context.getChangeBatchNo());
            break;
    }
}
```

## 优化效果

### 1. 代码可读性提升
- **清晰的方法命名**：每个方法的职责一目了然
- **消除深层嵌套**：通过方法拆分，避免了复杂的嵌套结构
- **逻辑分离**：不同的处理逻辑分别在不同的方法中

### 2. 可维护性提升
- **单一职责**：每个方法只负责一个特定的功能
- **易于扩展**：新增围栏变更类型只需添加新的枚举值和处理方法
- **减少重复**：公共逻辑提取到独立方法中

### 3. 可测试性提升
- **独立测试**：每个处理方法都可以独立进行单元测试
- **模拟测试**：可以轻松模拟不同的上下文进行测试
- **边界测试**：可以针对各种边界情况进行专门测试

### 4. 错误处理改进
- **精确定位**：异常发生时可以快速定位到具体的处理方法
- **统一处理**：消息发送逻辑统一处理，减少重复代码
- **日志优化**：更清晰的日志记录，便于问题排查

## 注意事项

1. **向后兼容**：优化后的方法签名保持不变，不影响调用方
2. **事务一致性**：所有处理逻辑仍在同一个事务中执行
3. **性能影响**：优化主要针对代码结构，对性能影响微乎其微
4. **测试覆盖**：建议为新的方法结构编写完整的单元测试

## 后续建议

1. **单元测试**：为每个新方法编写对应的单元测试
2. **集成测试**：验证整体流程的正确性
3. **代码审查**：团队成员review新的代码结构
4. **文档更新**：更新相关的技术文档和API文档
